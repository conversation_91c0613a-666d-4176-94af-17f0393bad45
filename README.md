# aws-addons

GitOps control plane repository defines the desired state of shared infrastructure components.

## Provided add-ons

- [aws-load-balancer-controller](https://kubernetes-sigs.github.io/aws-load-balancer-controller)
- [aws-privateca-issuer](https://github.com/cert-manager/aws-privateca-issuer)
- [cert-manager](https://cert-manager.io)
- [crossplane](https://www.crossplane.io/)
- [external-dns](https://kubernetes-sigs.github.io/external-dns)
- [external-secrets](https://external-secrets.io)
- [kyverno](https://kyverno.io)
- [gloo](https://docs.solo.io/gloo-edge/latest/)
- [istio](https://istio.io/latest/docs/)
- [reloader](https://github.com/stakater/Reloader)
- [wiz](https://www.wiz.io/)

## Repository structure

This repository contains the following directories:

- [argocd](./argocd/) - Argo CD boostrap uses App of Apps to deploy ApplicationSets with cluster add-ons.
- [defaults](./defaults/) - Defines default chart values for add-on.
- [environments](./environments/) - Defines resources to deploy per environment. Includes Helm value overrides from chart directory mentioned above.
- [clusters](./clusters/) - Defines resources specific to particular cluster, it overrides the environment.
```sh
.
├── argocd
│  └── addons
│     ├── <namespace>
│     │  └── <addon>.yaml
|     └── exclude
│        └── boostrap.yaml
├── defaults
|  └── <addon>.yaml
├── clusters
│  └── <cluster_name>
│     └── <addon>.yaml
├── environments
│  └── <environment>
│     └── <region>
│        └── <addon>.yaml
└── README.md
```
