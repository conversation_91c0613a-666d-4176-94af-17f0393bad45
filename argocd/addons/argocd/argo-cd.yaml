---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: argo-cd
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  argocd-ingress: 'true'
              values:
                chart: argo-cd
                chartRepository: ghcr.io/argoproj/argo-helm
                chartVersion: 8.5.8
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 8.5.8
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 8.5.8
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: 'improvement/ATL-4000'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: argocd
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              global:
                domain: '{{ index .metadata.annotations "argocd/domain" }}'
              server:
                ingress:
                  enabled: true
                  controller: aws
                  ingressClassName: alb
                  annotations:
                    alb.ingress.kubernetes.io/group.name: infra
                    alb.ingress.kubernetes.io/scheme: internet-facing
                    alb.ingress.kubernetes.io/target-type: ip
                    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
                    alb.ingress.kubernetes.io/ssl-redirect: '443'
                    external-dns.alpha.kubernetes.io/hostname: '{{ index .metadata.annotations "argocd/domain" }}'
                    alb.ingress.kubernetes.io/tags: "public_facing=true"
                  aws:
                    serviceType: ClusterIP
                    backendProtocolVersion: GRPC
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "argocd" (index .metadata.annotations "argocd/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
