---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: argo-cd-core
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  argocd-ingress: 'false'
              values:
                chart: argo-cd
                chartRepository: ghcr.io/argoproj/argo-helm
                chartVersion: 8.5.8
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 8.5.8
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 8.5.8
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: argocd
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "argocd" (index .metadata.annotations "argocd/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
