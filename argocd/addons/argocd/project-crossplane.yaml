apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: crossplane
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1000"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: Crossplane modules
  orphanedResources:
    warn: false
    ignore:
      - kind: ConfigMap
        name: istio-*
      - group: elbv2.k8s.aws
        kind: TargetGroupBinding
        name: '*'
  sourceRepos:
    - '*'  
  destinations:
    - namespace: crossplane-system
      server: 'https://kubernetes.default.svc'
  clusterResourceWhitelist:
    - group: '*'
      kind: '*'
