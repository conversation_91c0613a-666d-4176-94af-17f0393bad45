---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: observability
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1000"
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  description: Observability add-ons
  orphanedResources:
    warn: false
    ignore:
      - kind: ConfigMap
        name: istio-*
      - group: elbv2.k8s.aws
        kind: TargetGroupBinding
        name: '*'
  sourceRepos:
    - '*'
  destinations:
    - server: https://kubernetes.default.svc
      namespace: '*'
  clusterResourceWhitelist:
    - group: '*'
      kind: '*'
