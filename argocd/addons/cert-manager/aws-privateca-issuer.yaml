---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: aws-privateca-issuer
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  aws-privateca-issuer: 'true'
              values:
                chart: aws-privateca-issuer
                chartRepository: https://cert-manager.github.io/aws-privateca-issuer
                chartVersion: v1.7.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: v1.7.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: v1.7.0
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              serviceAccount:
                name: '{{ index .metadata.annotations "aws-privateca-issuer/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "aws-privateca-issuer/role-arn" }}'
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ default "https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git" .metadata.annotations.config_repository }}'
          targetRevision: '{{ default "main" .metadata.annotations.config_revision }}'
          helm:
            valuesObject:
              arn: '{{ .metadata.annotations.aws_pca }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "aws-privateca-issuer/namespace" }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
