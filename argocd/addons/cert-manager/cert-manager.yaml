---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: cert-manager
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-800"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              values:
                chart: cert-manager
                chartRepository: https://charts.jetstack.io
                chartVersion: 1.18.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 1.18.2
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 1.18.2
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "cert-manager" (index .metadata.annotations "cert-manager/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
      ignoreDifferences:
        - kind: Secret
          name: aws-load-balancer-tls
          jsonPointers: [/data]
        - group: admissionregistration.k8s.io
          kind: MutatingWebhookConfiguration
          jqPathExpressions: ['.webhooks[].clientConfig.caBundle']
        - group: admissionregistration.k8s.io
          kind: ValidatingWebhookConfiguration
          jqPathExpressions: ['.webhooks[].clientConfig.caBundle']