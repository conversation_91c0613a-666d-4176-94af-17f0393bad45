---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: crossplane-aws
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  crossplane: 'true'
              values:
                chart: crossplane-aws
                chartRepository: https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: main
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - path: charts/{{ .values.chart }}
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              serviceAccount:
                name: '{{ index .metadata.annotations "crossplane/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "crossplane/role-arn" }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "crossplane/namespace" }}'
      syncPolicy:
        retry:
          limit: 100
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
