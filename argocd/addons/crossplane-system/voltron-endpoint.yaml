---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: voltron-endpoint
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  crossplane: 'true'
                  voltron: 'true'
              values:
                chart: voltron-endpoint
                chartRepository: https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: main
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: crossplane
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - path: 'crossplane/{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              global:
                cluster: '{{ .metadata.annotations.cluster_name }}'
                aws:
                  account: '{{ .metadata.annotations.aws_account }}'
                  region: '{{ .metadata.annotations.aws_region }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "crossplane/namespace" }}'
      syncPolicy:
        retry:
          limit: 100
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
