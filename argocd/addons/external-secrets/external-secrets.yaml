---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: external-secrets
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-800"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              values:
                chart: external-secrets
                chartRepository: https://charts.external-secrets.io
                chartVersion: 0.20.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 0.20.2
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 0.20.2
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              serviceAccount:
                name: '{{ index .metadata.annotations "external-secrets/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "external-secrets/role-arn" }}'
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ default "https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git" .metadata.annotations.config_repository }}'
          targetRevision: '{{ default "main" .metadata.annotations.config_revision }}'
          helm:
            valuesObject:
              region: '{{ .metadata.annotations.aws_region }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "external-secrets/namespace" }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
