---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: istio
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  istio: "true"
              values:
                chart: istio
                chartRepository: https://istio-release.storage.googleapis.com/charts
                chartVersion: 1.27.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 1.27.1
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 1.27.1
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: base
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ default "https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git" .metadata.annotations.config_repository }}'
          targetRevision: '{{ default "main" .metadata.annotations.config_revision }}'
          helm:
            valuesObject:
              certificate:
                enabled: true
        - chart: istiod
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
        - chart: cni
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
        - chart: ztunnel
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: ztunnel
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "istio-system" (index .metadata.annotations "istio/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
      ignoreDifferences:
        - group: admissionregistration.k8s.io
          kind: ValidatingWebhookConfiguration
          jqPathExpressions: ['.webhooks[]?.failurePolicy']