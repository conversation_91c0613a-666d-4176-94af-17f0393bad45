---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: gateway-agora
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  gateways: "true"
              values:
                release: agora
                chart: istio-gateway
                chartRepository: https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: main
  template:
    metadata:
      name: gateway-agora
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.release }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/gateway-{{ .values.release }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              # Use non-standard pattern until we consolidate DNS zones
              # hostname: '{{ .metadata.annotations.stack }}.{{ .metadata.annotations.environment }}.vnext.veracode.net'
              hostname: '{{ .metadata.annotations.environment }}-{{ .metadata.annotations.stack }}.vnext.veracode.net'
              secretKey: '{{ .metadata.annotations.environment }}/shared/png-gateway-certificate'
              gateway:
                annotations:
                  service.beta.kubernetes.io/aws-load-balancer-name: '{{ .metadata.annotations.cluster_name }}-ag'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "kube-gateways" (index .metadata.annotations "istio/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
