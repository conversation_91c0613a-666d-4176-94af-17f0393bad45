---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: aws-eks-rbac
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1000"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  voltron: "true"
              values:
                chart: aws-eks-rbac
                chartRepository: https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git
                chartVersion: develop
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: develop
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: main
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: kube-system
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
