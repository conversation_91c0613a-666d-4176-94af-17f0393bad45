---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: aws-load-balancer-controller
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  aws-load-balancer-controller: 'true'
              values:
                chart: aws-load-balancer-controller
                chartRepository: https://aws.github.io/eks-charts
                chartVersion: 1.13.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 1.13.4
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 1.13.4
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              clusterName: '{{ .metadata.annotations.cluster_name }}'
              serviceAccount:
                name: '{{ index .metadata.annotations "aws-load-balancer-controller/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "aws-load-balancer-controller/role-arn" }}'
                  eks.amazonaws.com/sts-regional-endpoints: regional
              defaultTags:
                env: '{{ .metadata.annotations.environment }}'
                stack_name: '{{ .metadata.annotations.cluster_name }}'
                owner: '{{ .metadata.annotations.owner }}'
                email: <EMAIL>
                cost_center: vnext
                purpose: Ingress for K8s cluster
                product: platform
                solution_tier: k8s
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "aws-load-balancer-controller/namespace" }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
      ignoreDifferences:
        - kind: Secret
          name: aws-load-balancer-tls
          jsonPointers: [/data]
        - group: admissionregistration.k8s.io
          kind: MutatingWebhookConfiguration
          jqPathExpressions: ['.webhooks[].clientConfig.caBundle']
        - group: admissionregistration.k8s.io
          kind: ValidatingWebhookConfiguration
          jqPathExpressions: ['.webhooks[].clientConfig.caBundle']