---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: external-dns
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  external-dns: 'true'
              values:
                chart: external-dns
                chartRepository: https://kubernetes-sigs.github.io/external-dns
                chartVersion: 1.19.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 1.19.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 1.19.0
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              domainFilters:
                - '{{ .metadata.annotations.stack }}.{{ .metadata.annotations.environment }}.vnext.veracode.io'
                - '{{ .metadata.annotations.stack }}.{{ .metadata.annotations.environment }}.vnext.veracode.net'
              serviceAccount:
                name: '{{ index .metadata.annotations "external-dns/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "external-dns/role-arn" }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ index .metadata.annotations "external-dns/namespace" }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
