---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: gateway-api
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-1000"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              # K8s Gateway API CRDs
              values:
                revision: v1.3.0
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                revision: v1.3.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                revision: v1.3.0
  template:
    metadata:
      name: gateway-api
    spec:
      project: cluster-addons
      sources:
        - path: config/crd
          repoURL: https://github.com/kubernetes-sigs/gateway-api.git
          targetRevision: '{{ .values.revision }}'
      destination:
        name: '{{ .name }}'
        namespace: kube-system
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - ServerSideApply=true
