---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: karpenter
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              values:
                chart: karpenter
                chartRepository: public.ecr.aws/karpenter
                chartVersion: 1.6.1
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 1.6.1
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 1.6.1
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              settings:
                clusterName: '{{ .metadata.annotations.cluster_name }}'
                clusterEndpoint: '{{ .metadata.annotations.cluster_endpoint }}'
                interruptionQueue: '{{ index .metadata.annotations "karpenter/queue" }}'
                reservedENIs: 1
              serviceAccount:
                name: '{{ index .metadata.annotations "karpenter/service_account" }}'
                annotations:
                  eks.amazonaws.com/role-arn: '{{ index .metadata.annotations "karpenter/role-arn" }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "kube-system" (index .metadata.annotations "karpenter/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
