---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: metrics-server
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              values:
                chart: metrics-server
                chartRepository: https://kubernetes-sigs.github.io/metrics-server
                chartVersion: 3.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 3.13.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 3.13.0
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "kube-system" (index .metadata.annotations "metrics-server/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
      ignoreDifferences:
        - group: apiregistration.k8s.io
          kind: APIService
          jqPathExpressions: ['.spec.caBundle']
        - group: apiregistration.k8s.io
          kind: APIService
          jqPathExpressions: ['.spec.insecureSkipTLSVerify']