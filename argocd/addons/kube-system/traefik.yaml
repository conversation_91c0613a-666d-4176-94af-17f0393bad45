---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: traefik
  namespace: argocd
spec:
  goTemplate: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  traefik: 'true'
              values:
                chart: traefik
                chartRepository: https://traefik.github.io/charts
                chartVersion: 30.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 30.1.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 30.1.0
  template:
    metadata:
      name: '{{ .values.chart }}'
      finalizers:
        - resources-finalizer.argocd.argoproj.io
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              service:
                annotations:
                  external-dns.alpha.kubernetes.io/hostname: '*.infra.{{ .metadata.annotations.stack }}.{{ .metadata.annotations.environment }}.vnext.veracode.io'
                  service.beta.kubernetes.io/aws-load-balancer-name: '{{ .metadata.annotations.cluster_name }}-infra'
                  service.beta.kubernetes.io/aws-load-balancer-ssl-cert: '{{ .metadata.annotations.aws_acm }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "kube-system" (index .metadata.annotations "traefik/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
