---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: wiz
  namespace: argocd
spec:
  goTemplate: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              # Mandatory security add-on
              values:  
                release: wiz
                chart: wiz-kubernetes-integration
                chartRepository: https://charts.wiz.io/
                chartVersion: 0.2.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 0.2.125
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 0.2.125
  template:
    metadata:
      name: '{{ .values.release }}'
      finalizers:
        - resources-finalizer.argocd.argoproj.io
    spec:
      project: cluster-addons
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.release }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              wiz-sensor:
                sensorClusterName: '{{ .metadata.annotations.cluster_name }}'
              wiz-kubernetes-connector:
                autoCreateConnector:
                  connectorName: '{{ .metadata.annotations.cluster_name }}'
                  apiServerEndpoint: '{{ .metadata.annotations.cluster_endpoint }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "kube-system" (index .metadata.annotations "wiz/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
