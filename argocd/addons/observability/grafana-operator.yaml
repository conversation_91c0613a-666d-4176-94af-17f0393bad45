---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: grafana-operator
  namespace: argocd
  annotations:
    argocd.argoproj.io/sync-wave: "-100"
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  prometheus: 'true'
              values:
                chart: grafana-operator
                chartRepository: ghcr.io/grafana/helm-charts
                chartVersion: v5.19.4
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: v5.19.4
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: v5.19.4
  template:
    metadata:
      name: '{{ .values.chart }}'
    spec:
      project: observability
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "observability" (index .metadata.annotations "grafana/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
