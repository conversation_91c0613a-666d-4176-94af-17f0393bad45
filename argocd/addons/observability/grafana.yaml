---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: grafana
  namespace: argocd
spec:
  goTemplate: true
  syncPolicy:
    preserveResourcesOnDeletion: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  prometheus: 'true'
              values:
                release: grafana
                chart: grafana
                chartRepository: https://gitlab.laputa.veracode.io/vnext/core-services/aws-helm-charts.git
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: main
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: main
  template:
    metadata:
      name: grafana
      finalizers:
        - resources-finalizer.argocd.argoproj.io
    spec:
      project: observability
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: '{{ .metadata.annotations.addons_revision }}'
          ref: values
        - path: 'charts/{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: '{{ .values.chart }}'
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              grafana:
                ingress:
                  hosts:
                    - 'grafana.{{ .metadata.annotations.infra_domain }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "observability" (index .metadata.annotations "grafana/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
