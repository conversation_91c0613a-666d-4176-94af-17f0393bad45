---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: prometheus
  namespace: argocd
spec:
  goTemplate: true
  generators:
    - merge:
        mergeKeys: 
          - server
        generators:
          - clusters:
              selector:
                matchLabels:
                  prometheus: 'true'
              values:
                chart: kube-prometheus-stack
                chartRepository: https://prometheus-community.github.io/helm-charts
                chartVersion: 77.x
          - clusters:
              selector:
                matchLabels:
                  environment: preprod
              values:
                chartVersion: 77.5.0
          - clusters:
              selector:
                matchLabels:
                  environment: prod
              values:
                chartVersion: 77.5.0
  template:
    metadata:
      name: prometheus
      finalizers:
        - resources-finalizer.argocd.argoproj.io
    spec:
      project: observability
      sources:
        - repoURL: '{{ .metadata.annotations.addons_repository }}'
          targetRevision: 'improvement/ATL-4000'
          ref: values
        - chart: '{{ .values.chart }}'
          repoURL: '{{ .values.chartRepository }}'
          targetRevision: '{{ .values.chartVersion }}'
          helm:
            releaseName: prometheus
            ignoreMissingValueFiles: true
            valueFiles:
              - $values/defaults/{{ .values.chart }}.yaml
              - $values/environments/{{ .metadata.annotations.environment }}/{{ .metadata.annotations.aws_region }}/{{ .values.chart }}.yaml
              - $values/clusters/{{ .metadata.annotations.cluster_name }}/{{ .values.chart }}.yaml
            valuesObject:
              prometheus:
                ingress:
                  hosts:
                    - 'prometheus.{{ .metadata.annotations.infra_domain }}'
                prometheusSpec:
                  cluster_name: '{{ .metadata.annotations.cluster_name }}'
                  environment: '{{ .metadata.annotations.environment }}'
      destination:
        name: '{{ .name }}'
        namespace: '{{ default "observability" (index .metadata.annotations "prometheus/namespace") }}'
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true
          - ServerSideApply=true
