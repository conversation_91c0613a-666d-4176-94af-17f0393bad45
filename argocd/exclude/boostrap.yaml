---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: addons
  namespace: argocd
spec:
  generators:
    - clusters: {}
  template:
    metadata:
      name: addons
      finalizers:
        - resources-finalizer.argocd.argoproj.io
    spec:
      project: default
      revisionHistoryLimit: 3
      destination:
        namespace: argocd
        server: '{{ server }}'
      source:
        path: '{{ metadata.annotations.addons_path }}'
        repoURL: '{{ metadata.annotations.addons_repository }}'
        targetRevision: '{{ metadata.annotations.addons_revision }}'
        directory:
          recurse: true
          exclude: exclude/*
      syncPolicy:
        automated:
          allowEmpty: true
          prune: true
          selfHeal: true