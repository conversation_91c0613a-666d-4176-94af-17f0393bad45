##########################################################
# Argo CD configuration
# Ref: https://github.com/argoproj/argo-helm/blob/main/charts/argo-cd/values.yaml
##########################################################
global:
  logging:
    format: json
    level: info
  priorityClassName: system-cluster-critical
  affinity:
    nodeAffinity:
      matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
  tolerations:
    - key: veracode.io/infra
      operator: Exists

configs:
  # General Argo CD configuration
  ## Ref: https://github.com/argoproj/argo-cd/blob/master/docs/operator-manual/argocd-cm.yaml
  cm:
    # Disable local admin account
    admin.enabled: true
    accounts.admin: apiKey, login
    # Enable local read-only account login
    accounts.readonly: login
    # Disable anonymous access
    users.anonymous.enabled: false
    # Enable application statusbadge
    statusbadge.enabled: true
    # Customize K8s resources behaviour
    resource.compareoptions: | 
      ignoreAggregatedRoles: true
    resource.customizations.ignoreDifferences.all: |
      jqPathExpressions:
        - .spec.replicas
        - .spec.template.spec.containers[].env[].valueFrom.resourceFieldRef.divisor
        - .spec.template.spec.initContainers[].env[].valueFrom.resourceFieldRef.divisor
      managedFieldManagers:
        - external-secrets
    resource.customizations.ignoreDifferences.admissionregistration.k8s.io_MutatingWebhookConfiguration: |
      jqPathExpressions:
        - .webhooks[]?.clientConfig.caBundle
    resource.customizations.ignoreDifferences.admissionregistration.k8s.io_ValidatingWebhookConfiguration: |
      jqPathExpressions:
        - .webhooks[]?.clientConfig.caBundle

  # Argo CD RBAC policy configuration
  ## Ref: https://github.com/argoproj/argo-cd/blob/master/docs/operator-manual/rbac.md
  rbac:
    policy.default: role:readonly

  # Argo CD configuration parameters
  ## Ref: https://github.com/argoproj/argo-cd/blob/master/docs/operator-manual/argocd-cmd-params-cm.yaml
  params:
    # Number of application status processors
    controller.status.processors: 50
    # Number of application operation processors
    controller.opration.processors: 25
    # Run server without TLS (terminated on ingress)
    server.insecure: true

  # Argo CD sensitive data
  # Ref: https://argo-cd.readthedocs.io/en/stable/operator-manual/user-management/#sensitive-data-and-sso-client-secrets
  secret:
    createSecret: false

##########################################################
# Argo CD controller - Manages K8s clusters and resources
##########################################################
controller:
  # he number of application controller pods to run.
  # Additional replicas will cause sharding of managed clusters across number of replicas.
  replicas: 1

  # Resource limits and requests for the Argo CD controller
  resources:
    requests:
      cpu: 1
      memory: 1Gi

##########################################################
# Argo CD UI server
##########################################################
server:
  # Argo CD server Horizontal Pod Autoscaler
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 75
    targetMemoryUtilizationPercentage: 75

  # Argo CD server Pod Disruption Budget
  ## Ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
  pdb:
    enabled: true
    minAvailable: 60%

  # Resource limits and requests for the Argo CD server
  resources:
    requests:
      cpu: 200m
      memory: 256Mi

  # Argo UI extensions
  ## This function in tech preview stage, do expect instability or breaking changes in newer versions.
  ## Ref: https://github.com/argoproj-labs/argocd-extensions
  extensions:
    enabled: true
    resources:
      requests:
        cpu: 50m
        memory: 64Mi

##########################################################
# Repository server
##########################################################
repoServer:
  # Repo server Horizontal Pod Autoscaler
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 75
    targetMemoryUtilizationPercentage: 75

  # Repo server Pod Disruption Budget
  ## Ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
  pdb:
    enabled: true
    # Number of pods that are avilable after evition as percentage
    minAvailable: 60%

  # Resource limits and requests for the repo server pods
  resources:
    requests:
      cpu: 1
      memory: 1Gi

##########################################################
# ApplicationSet controller
##########################################################
applicationSet:
  # The number of ApplicationSet controller pods to run
  replicas: 3

  # ApplicationSet controller Pod Disruption Budget
  ## Ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
  pdb:
    enabled: true
    minAvailable: 1

  # Resource limits and requests for the ApplicationSet controller pods
  resources:
    requests:
      cpu: 250m
      memory: 250Mi

##############################################################
# Notification controller
# Ref: https://argocd-notifications.readthedocs.io/en/stable
##############################################################
notifications:
  enabled: false

##############################################################
# Redis in HA mode for caching
##############################################################
redis-ha:
  enabled: true
  hardAntiAffinity: true
  additionalAffinities:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
            - { key: veracode.io/role, operator: In, values: [ infra ] }
  redis:
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
    exporter:
      enabled: true
      image: bitnami/redis_exporter
  haproxy:
    hardAntiAffinity: true
    additionalAffinities:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
              - { key: veracode.io/role, operator: In, values: [ infra ] }
    podDisruptionBudget:
      minAvailable: 1
  tolerations:
    - key: veracode.io/infra
      operator: Exists

##############################################################
# Dex identity provider
##############################################################
dex:
  enabled: false
