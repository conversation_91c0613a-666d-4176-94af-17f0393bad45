##########################################################
# AWS Load Balancer Controller configuration
# Ref: https://github.com/kubernetes-sigs/aws-load-balancer-controller
##########################################################
replicaCount: 2
revisionHistoryLimit: 0

# Enable cert-manager for webhooks
enableCertManager: true
enableServiceMutatorWebhook: false

# Used with VPC CNI to route directly to pod
defaultTargetType: ip

# Security policy for TLS listeners
# Ref: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/create-https-listener.html
defaultSSLPolicy: ELBSecurityPolicy-TLS13-1-2-2021-06

# Enforce settings for ALB ingresses
ingressClassParams:
  spec:
    targetType: ip
    inboundCIDRs:
      - ***************/32
      - **************/32
      - **************/32
      - ***************/32
      - ***************/32
      - ***************/32
      - ***************/32
      - ***************/32
      - ************/32
      - ************/32
      - ************/32
      - *************/32
      - ************/32
      - ************/32
      - *************/32
      - **************/32
      - **************/32
      - ***************/32

# Controller configuration
controllerConfig:
  featureGates:
    # Disable check for kubernetes.io/cluster/${cluster-name}
    SubnetsClusterTagCheck: false

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 1
    maxUnavailable: 1

podDisruptionBudget:
  maxUnavailable: 1

resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 128Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: aws-load-balancer-controller
            app.kubernetes.io/name: aws-load-balancer-controller
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: aws-load-balancer-controller

tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - key: veracode.io/infra
    operator: Exists
