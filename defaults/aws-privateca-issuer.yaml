##########################################################
# AWS Private CA issuer configuration
# Ref: https://github.com/cert-manager/aws-privateca-issuer
##########################################################
revisionHistoryLimit: 3

replicaCount: 2

podDisruptionBudget:
  minAvailable: 1
  maxUnavailable: null

resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 10m
    memory: 32Mi

priorityClassName: system-cluster-critical

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/name: aws-privateca-issuer
            app.kubernetes.io/instance: aws-privateca-issuer
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: aws-privateca-issuer

tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - key: veracode.io/infra
    operator: Exists