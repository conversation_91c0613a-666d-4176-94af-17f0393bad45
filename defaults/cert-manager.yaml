##########################################################
# Certificate manager configuration
# Ref: https://github.com/cert-manager/cert-manager
##########################################################
global:
  revisionHistoryLimit: 0
  priorityClassName: system-cluster-critical

crds:
  enabled: true
  keep: true

# Remove secrets when Certificate resource is removed
enableCertificateOwnerRef: true

##########################################################
# Certificate manager
##########################################################
replicaCount: 3

strategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 0
    maxUnavailable: 1

podDisruptionBudget:
  enabled: true
  minAvailable: 1

resources:
  limits:
    cpu: 100m
    memory: 256Mi
  requests:
    cpu: 10m
    memory: 32Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: cert-manager
            app.kubernetes.io/component: controller
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: cert-manager
        app.kubernetes.io/component: controller

tolerations:
  - key: veracode.io/infra
    operator: Exists

##########################################################
# Resource validation webhook
##########################################################
webhook:
  replicaCount: 3

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1

  podDisruptionBudget:
    enabled: true
    minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 256Mi
    requests:
      cpu: 10m
      memory: 32Mi

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/instance: cert-manager
              app.kubernetes.io/component: webhook
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/instance: cert-manager
          app.kubernetes.io/component: webhook

  tolerations:
    - key: veracode.io/infra
      operator: Exists

##########################################################
# CA injector
# Ref: https://cert-manager.io/docs/concepts/ca-injector/
##########################################################
cainjector:
  replicaCount: 3

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1

  podDisruptionBudget:
    enabled: true
    minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 512Mi
    requests:
      cpu: 10m
      memory: 256Mi

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/instance: cert-manager
              app.kubernetes.io/component: cainjector
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/instance: cert-manager
          app.kubernetes.io/component: cainjector

  tolerations:
    - key: veracode.io/infra
      operator: Exists

startupapicheck:
  tolerations:
    - key: veracode.io/infra
      operator: Exists