##########################################################
# Crossplane configuration
# Ref: https://github.com/crossplane/crossplane/tree/v1.12.1/cluster/charts/crossplane
##########################################################
replicas: 3

priorityClassName: system-cluster-critical

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app: crossplane
        topologyKey: kubernetes.io/hostname
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app: crossplane
          topologyKey: topology.kubernetes.io/zone

tolerations:
  - key: veracode.io/infra
    operator: Exists

rbacManager:
  replicas: 3

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
            - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app: crossplane-rbac-manager
          topologyKey: kubernetes.io/hostname
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app: crossplane-rbac-manager
            topologyKey: topology.kubernetes.io/zone

  tolerations:
    - key: veracode.io/infra
      operator: Exists
