##########################################################
# External DNS configuration
# Ref: https://github.com/kubernetes-sigs/external-dns/blob/master/charts/external-dns/values.yaml
##########################################################
# Revision is controlled via GitOps
revisionHistoryLimit: 0

# Interval for DNS updates
interval: "5m"

# When enabled, triggers run loop on create/update/delete events in addition to regular interval
triggerLoopOnEvent: true

# Set identifier on created DNS records
txtOwnerId: external-dns

# Sync all records
policy: sync

# Resources to be observed by external-dns
sources:
  - crd
  - ingress
  - service
  - gateway-httproute
  # - gateway-grpcroute
  # - gateway-tlsroute
  # - gateway-tcproute
  # - gateway-udproute

extraArgs:
  # Limit Services watched by type
  - --service-type-filter=LoadBalancer

resources:  
  limits:
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 256Mi

deploymentStrategy:
  type: RollingUpdate
  
priorityClassName: system-cluster-critical

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: external-dns
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: external-dns

tolerations:
  - key: veracode.io/infra
    operator: Exists