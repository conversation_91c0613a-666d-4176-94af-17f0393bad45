##########################################################
# External Secrets Operator configuration
# Ref: https://github.com/external-secrets/external-secrets/tree/main/deploy/charts/external-secrets
##########################################################
replicaCount: 3
revisionHistoryLimit: 0
leaderElect: true

podDisruptionBudget:
  enabled: true
  minAvailable: 1

resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 10m
    memory: 32Mi

serviceAccount:
  name: external-secrets

serviceMonitor:
  enabled: false

priorityClassName: system-cluster-critical

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: external-secrets
        app.kubernetes.io/instance: external-secrets

tolerations:
  - key: veracode.io/infra
    operator: Exists

##########################################################
# Certificate controller
##########################################################
certController:
  replicaCount: 3
  revisionHistoryLimit: 0

  podDisruptionBudget:
    enabled: false
    minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 512Mi
    requests:
      cpu: 10m
      memory: 128Mi

  serviceAccount:
    create: false
    name: external-secrets

  serviceMonitor:
    enabled: false

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/name: external-secrets-cert-controller
              app.kubernetes.io/instance: external-secrets
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/name: external-secrets-cert-controller
          app.kubernetes.io/instance: external-secrets

  tolerations:
    - key: veracode.io/infra
      operator: Exists

##########################################################
# Validation webhook
##########################################################
webhook:
  replicaCount: 3
  revisionHistoryLimit: 0

  podDisruptionBudget:
    enabled: false
    minAvailable: 1

  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 10m
      memory: 32Mi

  serviceAccount:
    create: false
    name: external-secrets

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app.kubernetes.io/name: external-secrets-webhook
              app.kubernetes.io/instance: external-secrets
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/name: external-secrets-webhook
          app.kubernetes.io/instance: external-secrets

  tolerations:
    - key: veracode.io/infra
      operator: Exists
