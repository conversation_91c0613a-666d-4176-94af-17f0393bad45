##########################################################
# Crowdstrike Falcon sensor
# Ref: https://github.com/CrowdStrike/falcon-helm/tree/main/helm-charts/falcon-sensor
##########################################################
falcon:
  cid: DE413788848F4B92A4E8BCBF864197B4-19

node:
  backend: bpf
  
  image:
    repository: 199128305162.dkr.ecr.us-east-1.amazonaws.com/falcon-us/falcon-sensor
    tag: 7.28.0-18108-1.falcon-linux.Release.US-2
  daemonset:
    priorityClassCreate: false
    priorityClassName: system-node-critical

    resources:
      limits:
        cpu: 250m
        ephemeral-storage: 100Mi
        memory: 2Gi
      requests:
        cpu: 250m
        ephemeral-storage: 100Mi
        memory: 500Mi

    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: eks.amazonaws.com/compute-type
            operator: NotIn
            values:
              - fargate

    tolerations:
      # Make sure falcon gets scheduled on all nodes.
      - effect: NoSchedule
        operator: Exists
      # Mark the pod as a critical add-on for rescheduling.
      - key: CriticalAddonsOnly
        operator: Exists
      - effect: NoExecute
        operator: Exists