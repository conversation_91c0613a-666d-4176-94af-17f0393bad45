# Set from Argo CD until migrated
secretKey: ''

fullnameOverride: agora

extraObjects:
  - apiVersion: external-secrets.io/v1
    kind: ExternalSecret
    metadata:
      name: agora-tls
      annotations:
        argocd.argoproj.io/sync-wave: "-1"
    spec:
      secretStoreRef:
        kind: ClusterSecretStore
        name: aws-secrets-manager
      target:
        name: agora-tls
        template:
          type: kubernetes.io/tls
      data:
        - secretKey: ca.crt
          remoteRef:
            conversionStrategy: Default	
            decodingStrategy: None
            key: '{{ .Values.secretKey }}'
            metadataPolicy: None
            property: ca.crt
        - secretKey: tls.crt
          remoteRef:
            conversionStrategy: Default	
            decodingStrategy: None
            key: '{{ .Values.secretKey }}'
            metadataPolicy: None
            property: tls.crt
        - secretKey: tls.key
          remoteRef:
            conversionStrategy: Default	
            decodingStrategy: None
            key: '{{ .Values.secretKey }}'
            metadataPolicy: None
            property: tls.key
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      name: '{{ include "istio-gateway.fullname" . }}-remove-client-cert'
    spec:
      parentRefs:
        - group: gateway.networking.k8s.io	
          kind: Gateway
          name: agora
          sectionName: http
      hostnames:
        - '*.{{ .Values.hostname }}'
      rules:
        - filters:
          - type: RequestHeaderModifier
            requestHeaderModifier:
              remove:
                - x-forwarded-client-cert
          matches:	
          - path:	
              type: PathPrefix
              value: /

gateway:
  annotations:
    # external-dns.alpha.kubernetes.io/hostname: '*.{{ .Values.hostname }}'
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-scheme: internal
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-ip-address-type: dualstack
    service.beta.kubernetes.io/aws-load-balancer-attributes: load_balancing.cross_zone.enabled=true
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: deregistration_delay.timeout_seconds=15,deregistration_delay.connection_termination.enabled=true
  listeners:
    - name: https
      protocol: HTTPS
      port: 443
      tls:
        mode: Terminate
        certificateRefs:
          - group: ''	
            kind: Secret
            name: agora-tls
        options:
          gateway.istio.io/tls-terminate-mode: MUTUAL
      allowedRoutes:
        namespaces:
          from: All

autoscaling:
  enabled: true
