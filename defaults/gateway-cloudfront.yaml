fullnameOverride: cloudfront

extraObjects:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      name: '{{ include "istio-gateway.fullname" . }}-https-redirect'
    spec:
      parentRefs:
        - group: gateway.networking.k8s.io	
          kind: Gateway
          name: cloudfront
          sectionName: http
      hostnames:
        - 'cloudfront.{{ .Values.hostname }}'
      rules:
        - filters:
          - type: RequestRedirect
            requestRedirect:
              scheme: https
              statusCode: 302
          matches:	
          - path:	
              type: PathPrefix
              value: /

gateway:
  annotations:
    external-dns.alpha.kubernetes.io/hostname: 'cloudfront.{{ .Values.hostname }}'
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing 
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-ip-address-type: dualstack
    service.beta.kubernetes.io/aws-load-balancer-attributes: load_balancing.cross_zone.enabled=true
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: deregistration_delay.timeout_seconds=15,deregistration_delay.connection_termination.enabled=true
  listeners:
    - name: http
      port: 80
      protocol: HTTP
      hostname: 'cloudfront.{{ .Values.hostname }}'
      allowedRoutes:
        namespaces:
          from: All
    - name: https
      protocol: HTTP
      hostname: 'cloudfront.{{ .Values.hostname }}'
      port: 443
      allowedRoutes:
        namespaces:
          from: All

autoscaling:
  enabled: true
