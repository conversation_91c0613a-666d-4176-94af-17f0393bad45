##########################################################
# Grafana operator configuration
# Ref: https://github.com/grafana/grafana-operator/blob/master/deploy/helm/grafana-operator/values.yaml
##########################################################
# -- The number of operators to run simultaneously.
replicas: 2

resources:
  requests:
    cpu: 100m
    memory: 256Mi
  limits:
    cpu: 100m
    memory: 256Mi

# -- Pod priority class name
priorityClassName: system-cluster-critical

# -- Pod tolerations
tolerations:
  - key: veracode.io/infra
    operator: Exists

# -- Pod affinity
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/name: grafana-operator
            app.kubernetes.io/instance: grafana-operator
        topologyKey: kubernetes.io/hostname

extraObjects: []