global:
  securityContext:
    fsGroup: 10001

  persistence:
    enabled: true
    storageClassName: gp3
    resources:
      requests:
        storage: 10Gi

  config:
    security:
      admin_user: admin
      admin_password: grafana

grafana:
  revisionHistoryLimit: 0
  
  deploymentStrategy:
    type: Recreate

  ingress:
    enabled: true
    ingressClassName: alb
    annotations:
      alb.ingress.kubernetes.io/group.name: infra
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/healthcheck-path: /api/health
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/tags: "public_facing=true"

  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 256Mi

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app: grafana
          topologyKey: kubernetes.io/hostname

  tolerations:
    - key: veracode.io/infra
      operator: Exists

  volumes:
    - name: grafana-data
      persistentVolumeClaim:
        claimName: grafana-pvc

datasources:
  prometheus:
    name: prometheus
    type: prometheus
    access: proxy
    url: http://prometheus-operated:9090
    isDefault: true
    jsonData:
      "tlsSkipVerify": true
      "timeInterval": "5s"

dashboards:
  kubernetes-dashboard:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 18283
  k8s-views-pods:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15760
  k8s-views-nodes:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15759
  k8s-views-namespaces:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15758
  k8s-views-global:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15757
  k8s-system-coredns:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15762
  k8s-kube-prometheus-stack:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 19105
  k8s-system-api-server:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 15761
  kube-pod-overview:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 6781
  k8s-pod-metrics:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 747
  k8s-pod-monitoring:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 9144
  k8s-pod-metrics-details:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 21548
  k8s-workloads-cpu-memory-metrics:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 20372
  k8s-cluster-health:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 5502
  k8s-cluster-ram-and-cpu:
    folder: Performance Engineering
    datasources:
      - inputName: DS_PROMETHEUS
        datasourceName: prometheus
      - inputName: DS_SERVICEMONITOR
        datasourceName: prometheus
    grafanaCom:
      id: 16734