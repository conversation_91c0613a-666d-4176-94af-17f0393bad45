##########################################################
# Istio overrides for AWS
##########################################################
global:
  proxy: 
    # Bypass Istio for VPC services (RDS, Redis, etc.)
    # TODO verify if this is needed with ambient
    excludeIPRanges: 10.0.0.0/8
    # Resources used by proxy / gateways
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: "2"
        memory: 1Gi

  logAsJson: true
  priorityClassName: system-cluster-critical
  variant: distroless

  # A minimal set of requested resources to applied to all deployments so that
  # Horizontal Pod Autoscaler will be able to function (if set).
  # Each component can overwrite these default values by adding its own resources
  # block in the relevant section below and setting the desired resources values.
  defaultResources:
    limits:
      cpu: 100m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 256Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: eks.amazonaws.com/compute-type, operator: NotIn, values: [ fargate ] }

###########################################################
# Runtime configuration of components, including Istiod and istio-agent behavior
# See https://istio.io/docs/reference/config/istio.mesh.v1alpha1/ for all available options
##########################################################
meshConfig:
  # enableTracing: true
  # TODO:
  # discoverySelectors:
  # - matchLabels:
  #    istio-discovery: enabled
  # - matchLabels:
  #    istio-injection: enabled
  defaultConfig:
    gatewayTopology:
      numTrustedProxies: 2
    proxyMetadata:
      ISTIO_META_ENABLE_HBONE: "true"
  defaultProviders:       
    metrics:	
    - prometheus     
    accessLogging:
    - accessLog
  extensionProviders:
    - name: accesslog
      envoyFileAccessLog:
        path: /dev/stdout
        logFormat:
          labels:
            start_time: "%START_TIME%"
            source_host: "%HOSTNAME%"
            remote_adddres: "%DOWNSTREAM_REMOTE_ADDRESS_WITHOUT_PORT%"
            remote_address_direct: "%DOWNSTREAM_DIRECT_REMOTE_ADDRESS_WITHOUT_PORT%"
            user_agent: "%REQ(USER-AGENT)%"
            host: "%REQ(:AUTHORITY)%"
            protocol: "%PROTOCOL%"
            route: "%ROUTE_NAME%"
            sni: "%REQUESTED_SERVER_NAME%"
            upstream_host: "%UPSTREAM_HOST%"
            upstream_cluster: "%UPSTREAM_CLUSTER%"
            upstream_error: "%UPSTREAM_TRANSPORT_FAILURE_REASON%"
            termination_details: "%CONNECTION_TERMINATION_DETAILS%"
            request:
              request_method: "%REQ(:METHOD)%"
              request_path: "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%"
              request_time: "%DURATION%"
              request_xff: "%REQ(X-FORWARDED-FOR)%"
              request_id: "%REQ(X-REQUEST-ID)%"
              request_bytes_rcvd: "%BYTES_RECEIVED%"
            response:
              response_code: "%RESPONSE_CODE%"
              response_code_details: "%RESPONSE_CODE_DETAILS%"
              response_bytes_sent: "%BYTES_SENT%"
              response_flags: "%RESPONSE_FLAGS%"
              response_time: "%RESPONSE_DURATION%"
              response_upstream_time: "%RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)%"

##########################################################
# Istio CNI plugin
##########################################################
cni:
  enabled: true
  ambient:
    enabled: true
    reconcileIptablesOnStartup: true

##########################################################
# Istio control plane
##########################################################
pilot:
  autoscaleMin: 3
  autoscaleMax: 5
  env:
    AUTO_RELOAD_PLUGIN_CERTS: "true"
    PILOT_ENABLE_AMBIENT: "true"
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchLabels:
              app: istiod
          topologyKey: kubernetes.io/hostname
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          podAffinityTerm:
            labelSelector:
              matchLabels:
                app: istiod
            topologyKey: topology.kubernetes.io/zone
  tolerations:
    - key: veracode.io/infra
      operator: Exists
