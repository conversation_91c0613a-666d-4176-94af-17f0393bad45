##########################################################
# Karpenter configuration
# Ref: https://github.com/aws/karpenter/tree/main/charts/karpenter
##########################################################
revisionHistoryLimit: 0

controller:
  # Ensure that we fully utilize the maximum amount of resources that are supplied by
  # Fargate https://docs.aws.amazon.com/eks/latest/userguide/fargate-pod-configuration.html
  # Fargate adds 256 MB to each pod's memory reservation for the required Kubernetes
  # components (kubelet, kube-proxy, and containerd). Fargate rounds up to the following
  # compute configuration that most closely matches the sum of vCPU and memory requests in
  # order to ensure pods always have the resources that they need to run.
  resources:
    requests:
      cpu: 1
      memory: 1792Mi
    limits:
      cpu: 1
      memory: 1792Mi

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway

tolerations:
  - key: CriticalAddonsOnly
    operator: Exists
  - key: veracode.io/karpenter
    operator: Exists
    effect: NoSchedule
