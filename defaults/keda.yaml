##########################################################
# KEDA configuration
# Ref: https://github.com/kedacore/charts/blob/main/keda/values.yaml
##########################################################
certificates:
  certManager:
    enabled: true

operator:
  replicaCount: 2

webhooks:
  replicaCount: 2

resources:
  operator:
    limits:
      cpu: 1
      memory: 1000Mi
    requests:
      cpu: 100m
      memory: 100Mi
  metricServer:
    limits:
      cpu: 1
      memory: 1000Mi
    requests:
      cpu: 100m
      memory: 100Mi
  webhooks:
    limits:
      cpu: 1
      memory: 1000Mi
    requests:
      cpu: 100m
      memory: 100Mi

priorityClassName: system-cluster-critical

nodeSelector:
  veracode.io/role: infra

tolerations:
  - key: veracode.io/infra
    operator: Exists

topologySpreadConstraints:
  operator:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/name: keda-operator
  webhooks:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/name: keda-admission-webhooks



