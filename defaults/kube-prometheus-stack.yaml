##########################################################
# Prometheus stack configuration
# Ref: https://github.com/prometheus-community/helm-charts/blob/main/charts/kube-prometheus-stack/values.yaml
##########################################################

##########################################################
# Configuration for alertmanager
# ref: https://prometheus.io/docs/alerting/alertmanager/
##########################################################
alertmanager:
  enabled: false

##########################################################
## Create default rules for monitoring the cluster
##########################################################
defaultRules:
  rules:
    etcd: false
  ## Disabled PrometheusRule alerts
  disabled: {}
  # KubeAPIDown: true
  # NodeRAIDDegraded: true

##########################################################
## Configuration for kube-state-metrics subchart
##########################################################
kube-state-metrics:
  replicas: 2
  revisionHistoryLimit: 0
  resources:
    limits:
      cpu: 200m
      memory: 1Gi
    requests:
      cpu: 10m
      memory: 128Mi
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
  tolerations:
    - key: veracode.io/infra
      operator: Exists
  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
      labelSelector:
        matchLabels:
          app.kubernetes.io/name: kube-state-metrics

##########################################################
## Configuration for prometheus-node-exporter subchart
##########################################################
prometheus-node-exporter:
  revisionHistoryLimit: 0
  priorityClassName: system-node-critical
  prometheus:
    monitor:
      relabelings:
        - action: replace
          sourceLabels: [__meta_kubernetes_pod_node_name]
          targetLabel: instance
  resources:
    limits:
      cpu: 200m
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 32Mi

##########################################################
## Manages Prometheus and Alertmanager components
##########################################################
prometheusOperator:
  revisionHistoryLimit: 0
  admissionWebhooks:
    annotations:
      argocd.argoproj.io/hook: PreSync
      argocd.argoproj.io/hook-delete-policy: HookSucceeded
    mutatingWebhookConfiguration:
      annotations:
        argocd.argoproj.io/hook: PreSync
    validatingWebhookConfiguration:
      annotations:
        argocd.argoproj.io/hook: PreSync
    certManager:
      enabled: true
  resources:
    limits:
      cpu: 200m
      memory: 500Mi
    requests: 
      cpu: 100m
      memory: 100Mi
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
  tolerations:
    - key: veracode.io/infra
      operator: Exists
  verticalPodAutoscaler:
    enabled: false

##########################################################
## Deploy a Prometheus instance
##########################################################
prometheus:
  ingress:
    enabled: true
    ingressClassName: alb
    annotations:
      alb.ingress.kubernetes.io/group.name: infra
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/healthcheck-path: /-/healthy
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/tags: "public_facing=true"
    pathType: Prefix
  ## Settings affecting prometheusSpec
  ## ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md#prometheusspec
  prometheusSpec:
    replicas: 2
    enableAdminAPI: true
    logformat: json
    retention: 10d
    scrapeInterval: 60s
    ruleSelectorNilUsesHelmValues: false
    serviceMonitorSelectorNilUsesHelmValues: false
    podMonitorSelectorNilUsesHelmValues: false
    probeSelectorNilUsesHelmValues: false
    resources:
      requests:
        cpu: 1000m
        memory: 5Gi
      limits:
        cpu: 1500m
        memory: 20Gi
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
          - matchExpressions:
            - { key: veracode.io/role, operator: In, values: [ infra ] }
    tolerations:
      - key: veracode.io/infra
        operator: Exists
    topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: topology.kubernetes.io/zone
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            app.kubernetes.io/name: prometheus
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: 'gp3'
          accessModes: ['ReadWriteOnce']
          resources:
            requests:
              storage: 40Gi

##########################################################
## Disabled components located on AWS EKS control plane
##########################################################
kubeControllerManager:
  enabled: false
kubeScheduler:
  enabled: false
kubeProxy:
  enabled: false
grafana:
  enabled: false