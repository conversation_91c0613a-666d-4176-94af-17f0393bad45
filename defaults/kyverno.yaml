##########################################################
# Kyverno configuration
# Ref: https://github.com/kyverno/kyverno/blob/kyverno-chart-3.2.5/charts/kyverno/values.yaml
##########################################################
global:
  tolerations:
    - key: veracode.io/infra
      operator: Exists

# Features configuration
features:
  logging:
    # -- Logging format
    format: json
    # -- Logging verbosity
    verbosity: 2
  forceFailurePolicyIgnore:
    # -- Ignore webhook failures
    # Ref: https://github.com/kyverno/kyverno/issues/8390
    enabled: true

##########################################################
# Admission controller
##########################################################
admissionController:
  # -- (int) Desired number of pods
  replicas: 2

  initContainer:
    # Admission controller init resources
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
      requests:
        cpu: 10m
        memory: 64Mi

  container:
    # Admission controller resources
    resources:
      limits:
        memory: 1Gi
      requests:
        cpu: 100m
        memory: 256Mi

  # -- Optional priority class
  priorityClassName: system-cluster-critical

  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: kyverno
        app.kubernetes.io/component: admission-controller

##########################################################
# Background controller
##########################################################
backgroundController:
  # -- (int) Desired number of pods
  replicas: 2

  # Background controller resources
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 1
      memory: 512Mi

  # -- Optional priority class
  priorityClassName: system-cluster-critical

  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: kyverno
        app.kubernetes.io/component: background-controller

##########################################################
# Cleanup controller
##########################################################
cleanupController:
  # -- (int) Desired number of pods
  replicas: 2

  # Background controller resources
  resources:
    limits:
      memory: 128Mi
    requests:
      cpu: 100m
      memory: 64Mi

  # -- Optional priority class
  priorityClassName: system-cluster-critical

  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: kyverno
        app.kubernetes.io/component: cleanup-controller

##########################################################
# Reports controller
##########################################################
reportsController:
  # -- (int) Desired number of pods
  replicas: 2

  # Background controller resources
  resources:
    limits:
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 64Mi

  # -- Optional priority class
  priorityClassName: system-cluster-critical

  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: kyverno
        app.kubernetes.io/component: reports-controller
