##########################################################
# Metrics server configuration
# Ref: https://github.com/kubernetes-sigs/metrics-server
##########################################################
replicas: 2

updateStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxSurge: 0
    maxUnavailable: 1

podDisruptionBudget:
  enabled: true
  minAvailable: 1

args:
  - --node-selector=eks.amazonaws.com/compute-type!=fargate

apiService:
  insecureSkipTLSVerify: false

tls:
  type: cert-manager

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/name: metrics-server
            app.kubernetes.io/instance: metrics-server
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/instance: metrics-server

tolerations:
  - key: veracode.io/infra
    operator: Exists
    effect: NoSchedule
