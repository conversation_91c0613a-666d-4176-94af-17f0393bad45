##########################################################
# Reloader configuration
# Ref: https://github.com/stakater/Reloader
##########################################################
reloader:
  enableHA: true
  deployment:
    replicas: 3
    revisionHistoryLimit: 0

    priorityClassName: system-cluster-critical

    podDisruptionBudget:
      enabled: true
      minAvailable: 1

    resources:
      limits:
        cpu: 100m
        memory: 512Mi
      requests:
        cpu: 10m
        memory: 128Mi

    containerSecurityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      capabilities:
        drop:
          - ALL

    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
          - matchExpressions:
            - { key: veracode.io/role, operator: In, values: [ infra ] }

    topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: topology.kubernetes.io/zone
        whenUnsatisfiable: DoNotSchedule
        labelSelector:
          matchLabels:
            app.kubernetes.io/instance: reloader

    tolerations:
      - key: veracode.io/infra
        operator: Exists