##########################################################
# Treafik configuration
# Ref: https://github.com/traefik/traefik-helm-chart
##########################################################
globalArguments:
  - --global.checknewversion=false
  - --global.sendanonymoususage=false

logs:
  general:
    level: WARN
    format: json
  access:
    enabled: true
    format: json

providers:
  kubernetesCRD:
    enabled: false
    ingressClass: traefik-infra

  kubernetesIngress:
    ingressClass: traefik-infra
    publishedService:
      enabled: true

ingressClass:
  enabled: true
  isDefaultClass: false
  name: traefik-infra

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 60
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 60

podDisruptionBudget:
  enabled: true
  minAvailable: 30%

resources:
  limits:
    cpu: 100m
    memory: 256Mi
  requests:
    cpu: 100m
    memory: 256Mi

ports:
  web:
    # Permanent http -> https redirection
    redirectTo:
      port: websecure
  websecure:
    tls:
      enabled: true

service:
  enabled: true
  type: LoadBalancer
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: external
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    service.beta.kubernetes.io/aws-load-balancer-ip-address-type: ipv4
    service.beta.kubernetes.io/aws-load-balancer-attributes: load_balancing.cross_zone.enabled=true
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: deregistration_delay.timeout_seconds=15,deregistration_delay.connection_termination.enabled=true,preserve_client_ip.enabled=true
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: ssl
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: aws:acm::xxxxxxxxxx
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: '443'

priority-class-name: system-cluster-critical

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
          - { key: veracode.io/role, operator: In, values: [ infra ] }
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app: '{{ template "traefik.name" . }}'
        topologyKey: kubernetes.io/hostname

topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: topology.kubernetes.io/zone
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app: '{{ template "traefik.name" . }}'

tolerations:
  - key: veracode.io/infra
    operator: Exists
