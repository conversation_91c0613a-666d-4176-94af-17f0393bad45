global:
  wizApiToken:
    clientId: r7bhqdcfnvd7thqorvdcmrsw2ff2a7f4cuive3exip66vdmh56qwm
    clientToken: U3BCLyKc2sH43g5FqzYZWakZtAMZQ9CdnXbrS1oM83CYLWLsHNtMYcrFt5KjPiiD
    secret:
      annotations:
        argocd.argoproj.io/hook-delete-policy: HookFailed

wiz-kubernetes-connector:
  enabled: true

  wiz-broker:
    enabled: false

  autoCreateConnector:
    clusterFlavor: EKS
    createJobAnnotations:
      argocd.argoproj.io/hook-delete-policy: HookFailed      

wiz-sensor:
  enabled: true

  imagePullSecret:
    username: wizio-repo-8fc2780c-456d-47f9-9e0e-8d46264656d1
    password: ThaGhuxyV7sT4af147iX01oLMfKBiGcv+FpJHs0gwc+ACRA5Wf1b

  daemonset:
    priorityClassName: system-node-critical
    # Match only nodes managed by Karp<PERSON>
    nodeSelector:
      karpenter.sh/registered: "true"
    tolerations:
      # Make sure wiz-sensor gets scheduled on all nodes.
      - effect: NoSchedule
        operator: Exists
      # Mark the pod as a critical add-on for rescheduling.
      - key: CriticalAddonsOnly
        operator: Exists
      - effect: NoExecute
        operator: Exists